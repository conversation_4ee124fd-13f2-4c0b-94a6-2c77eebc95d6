import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MemberService } from '../../services/member.service';
import { ToastrService } from 'ngx-toastr';
import { 
  MembershipSelectionForDelete, 
  ActiveMembershipInfo 
} from '../../models/member-filter-enhanced.model';
import { faExclamationTriangle, faTrashAlt, faMoneyBillWave, faCalendarAlt, faInfoCircle } from '@fortawesome/free-solid-svg-icons';

export interface SmartDeleteDialogData {
  memberID: number;
  memberName: string;
}

export interface MemberActiveMemberships {
  memberID: number;
  memberName: string;
  phoneNumber: string;
  email: string;
  balance: number;
  activeMemberships: ActiveMembershipDetail[];
  totalActiveMemberships: number;
  totalRemainingDays: number;
  totalPaidAmount: number;
}

export interface ActiveMembershipDetail {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  day: number;
  price: number;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  isFrozen: boolean;
  freezeStartDate?: Date;
  freezeEndDate?: Date;
  creationDate: Date;
  updatedDate?: Date;
  payments: PaymentInfo[];
  totalPaid: number;
  remainingDebt: number;
  lastPaymentDate?: Date;
  lastPaymentMethod?: string;
  hasPendingPayments: boolean;
  isRecentlyCreated: boolean;
  deletionRiskLevel: string;
  deletionWarningMessage: string;
}

export interface PaymentInfo {
  paymentID: number;
  paymentAmount: number;
  paymentMethod: string;
  paymentStatus: string;
  paymentDate: Date;
  canBeRefunded: boolean;
}

export interface MembershipDeleteRequest {
  membershipID: number;
  deleteReason: string;
  confirmDeletion: boolean;
  processRefund: boolean;
  adminNotes: string;
}

@Component({
  selector: 'app-smart-delete-dialog',
  templateUrl: './smart-delete-dialog.component.html',
  styleUrls: ['./smart-delete-dialog.component.css'],
  standalone: false
})
export class SmartDeleteDialogComponent implements OnInit {
  // Font Awesome Icons
  faExclamationTriangle = faExclamationTriangle;
  faTrashAlt = faTrashAlt;
  faMoneyBillWave = faMoneyBillWave;
  faCalendarAlt = faCalendarAlt;
  faInfoCircle = faInfoCircle;

  // Data Properties
  memberData: MemberActiveMemberships | null = null;
  selectedMembershipId: number | null = null;
  deleteReason: string = '';
  processRefund: boolean = false;
  adminNotes: string = '';
  confirmDeletion: boolean = false;

  // UI State
  isLoading: boolean = false;
  step: 'loading' | 'selection' | 'confirmation' | 'processing' = 'loading';

  // Delete Reasons
  deleteReasons = [
    'Üye talebi',
    'Ödeme sorunu',
    'Sağlık problemi',
    'Taşınma',
    'Memnuniyetsizlik',
    'Diğer'
  ];

  constructor(
    public dialogRef: MatDialogRef<SmartDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SmartDeleteDialogData,
    private memberService: MemberService,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadMemberActiveMemberships();
  }

  /**
   * Üyenin aktif üyeliklerini yükle
   */
  loadMemberActiveMemberships(): void {
    this.isLoading = true;
    this.step = 'loading';

    this.memberService.getMemberActiveMemberships(this.data.memberID).subscribe({
      next: (response) => {
        if (response.success) {
          this.memberData = response.data;
          this.step = 'selection';
        } else {
          this.toastrService.error(response.message || 'Üyelik bilgileri yüklenemedi');
          this.dialogRef.close(false);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading member memberships:', error);
        this.toastrService.error('Üyelik bilgileri yüklenirken bir hata oluştu');
        this.dialogRef.close(false);
        this.isLoading = false;
      }
    });
  }

  /**
   * Üyelik seçildiğinde
   */
  onMembershipSelected(membershipId: number): void {
    this.selectedMembershipId = membershipId;
  }

  /**
   * Silme işlemini başlat
   */
  startDeletion(): void {
    if (!this.selectedMembershipId) {
      this.toastrService.warning('Lütfen silinecek üyeliği seçin');
      return;
    }

    if (!this.deleteReason) {
      this.toastrService.warning('Lütfen silme nedenini belirtin');
      return;
    }

    this.step = 'confirmation';
  }

  /**
   * Silme işlemini onayla ve gerçekleştir
   */
  confirmAndDelete(): void {
    if (!this.confirmDeletion) {
      this.toastrService.warning('Lütfen silme işlemini onaylayın');
      return;
    }

    this.step = 'processing';
    this.isLoading = true;

    const deleteRequest: MembershipDeleteRequest = {
      membershipID: this.selectedMembershipId!,
      deleteReason: this.deleteReason,
      confirmDeletion: this.confirmDeletion,
      processRefund: this.processRefund,
      adminNotes: this.adminNotes
    };

    this.memberService.deleteMembershipSafely(deleteRequest).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success('Üyelik başarıyla silindi');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message || 'Silme işlemi başarısız');
          this.step = 'confirmation';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error deleting membership:', error);
        this.toastrService.error('Silme işlemi sırasında bir hata oluştu');
        this.step = 'confirmation';
        this.isLoading = false;
      }
    });
  }

  /**
   * Geri git
   */
  goBack(): void {
    if (this.step === 'confirmation') {
      this.step = 'selection';
    } else if (this.step === 'selection') {
      this.dialogRef.close(false);
    }
  }

  /**
   * Dialog'u kapat
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * Seçilen üyeliği al
   */
  getSelectedMembership(): ActiveMembershipDetail | null {
    if (!this.memberData || !this.selectedMembershipId) {
      return null;
    }
    return this.memberData.activeMemberships.find(m => m.membershipID === this.selectedMembershipId) || null;
  }

  /**
   * Risk seviyesine göre CSS class döndür
   */
  getRiskClass(riskLevel: string): string {
    switch (riskLevel) {
      case 'HIGH': return 'risk-high';
      case 'MEDIUM': return 'risk-medium';
      case 'LOW': return 'risk-low';
      default: return 'risk-low';
    }
  }

  /**
   * Risk seviyesine göre ikon döndür
   */
  getRiskIcon(riskLevel: string): any {
    switch (riskLevel) {
      case 'HIGH': return faExclamationTriangle;
      case 'MEDIUM': return faInfoCircle;
      case 'LOW': return faInfoCircle;
      default: return faInfoCircle;
    }
  }

  /**
   * Ödeme durumuna göre badge class döndür
   */
  getPaymentStatusClass(status: string): string {
    switch (status) {
      case 'Completed': return 'badge-success';
      case 'Pending': return 'badge-warning';
      case 'Failed': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  /**
   * Tarih formatla
   */
  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('tr-TR');
  }

  /**
   * Para formatla
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  }
}
