import { Component, OnInit, OnDestroy } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { MembershipTypeService } from '../../services/membership-type.service';
import { MembershipService } from '../../services/membership.service';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { DialogService } from '../../services/dialog.service';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { 
  MemberFilterEnhanced, 
  EnhancedMemberPagingParameters, 
  BranchSummary,
  PackageDetail,
  BranchPackageFilter,
  PackageTypeInfo
} from '../../models/member-filter-enhanced.model';
import { MembershipType } from '../../models/membershipType';
import { faEdit, faSnowflake, faTrashAlt, faEye, faEyeSlash, faFilter, faUsers } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-member-filter-enhanced',
  templateUrl: './member-filter-enhanced.component.html',
  styleUrls: ['./member-filter-enhanced.component.css'],
  standalone: false
})
export class MemberFilterEnhancedComponent implements OnInit, OnDestroy {
  // Font Awesome Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSnowflake = faSnowflake;
  faEye = faEye;
  faEyeSlash = faEyeSlash;
  faFilter = faFilter;
  faUsers = faUsers;

  // Data Properties
  members: MemberFilterEnhanced[] = [];
  membershipTypes: MembershipType[] = [];
  branchPackageFilters: BranchPackageFilter[] = [];
  
  // Filter Properties
  memberFilterText: string = '';
  genderFilter: string = '';
  selectedBranch: string = '';
  selectedPackageType: string = '';
  minRemainingDays?: number;
  maxRemainingDays?: number;
  
  // UI State Properties
  isLoading: boolean = false;
  showPackageDetails: boolean = false;
  viewMode: 'branch' | 'package' = 'branch'; // Seviye 1 veya Seviye 2
  
  // Pagination Properties
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  pageSize = 20;
  
  // Search Subject
  private searchTextSubject = new Subject<string>();
  
  // Statistics
  totalActiveMembers: number = 0;
  genderCounts = { all: 0, male: 0, female: 0 };
  branchCounts: { [key: string]: number } = {};

  constructor(
    private memberService: MemberService,
    private membershipTypeService: MembershipTypeService,
    private membershipService: MembershipService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService
  ) {
    this.searchTextSubject.pipe(
      debounceTime(750),
      distinctUntilChanged()
    ).subscribe(searchText => {
      this.memberFilterText = searchText;
      this.currentPage = 1;
      this.loadMembers();
    });
  }

  ngOnInit(): void {
    this.getBranches();
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  ngOnDestroy(): void {
    this.searchTextSubject.complete();
  }

  /**
   * Branş ve paket türlerini yükle
   */
  getBranches(): void {
    this.membershipTypeService.getBranchesAndTypes().subscribe({
      next: (response) => {
        if (response.success) {
          this.membershipTypes = response.data;
          this.buildBranchPackageFilters();
        }
      },
      error: (error) => {
        console.error('Error fetching branches:', error);
      }
    });
  }

  /**
   * Branş ve paket filtrelerini oluştur
   */
  buildBranchPackageFilters(): void {
    const branchMap = new Map<string, PackageTypeInfo[]>();
    
    this.membershipTypes.forEach(mt => {
      if (!branchMap.has(mt.branch)) {
        branchMap.set(mt.branch, []);
      }
      
      branchMap.get(mt.branch)!.push({
        membershipTypeID: mt.membershipTypeID,
        typeName: mt.typeName,
        memberCount: 0, // Bu değer loadMembers'da güncellenecek
        averageRemainingDays: 0
      });
    });

    this.branchPackageFilters = Array.from(branchMap.entries()).map(([branch, packages]) => ({
      branch,
      packages,
      totalMembers: 0
    }));
  }

  /**
   * Üyeleri yükle
   */
  loadMembers(): void {
    this.isLoading = true;
    
    const parameters: EnhancedMemberPagingParameters = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchText: this.memberFilterText || undefined,
      gender: this.genderFilter ? parseInt(this.genderFilter) : undefined,
      branch: this.selectedBranch || undefined,
      membershipTypeFilter: this.selectedPackageType || undefined,
      showPackageDetails: this.showPackageDetails,
      minRemainingDays: this.minRemainingDays,
      maxRemainingDays: this.maxRemainingDays
    };

    this.memberService.getMemberDetailsEnhancedPaginated(parameters).subscribe({
      next: (response) => {
        if (response.success) {
          this.members = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
          this.calculateStatistics();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching members:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  /**
   * İstatistikleri hesapla
   */
  calculateStatistics(): void {
    this.genderCounts = { all: 0, male: 0, female: 0 };
    this.branchCounts = {};

    this.members.forEach(member => {
      this.genderCounts.all++;
      if (member.gender === 1) this.genderCounts.male++;
      if (member.gender === 2) this.genderCounts.female++;

      member.branchSummaries.forEach(branch => {
        this.branchCounts[branch.branch] = (this.branchCounts[branch.branch] || 0) + 1;
      });
    });
  }

  /**
   * Toplam aktif üye sayısını al
   */
  getTotalActiveMembers(): void {
    this.memberService.getTotalActiveMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalActiveMembers = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total active members:', error);
      }
    });
  }

  /**
   * Arama metni değiştiğinde tetiklenir
   */
  searchTextChanged(searchText: string): void {
    this.searchTextSubject.next(searchText);
  }

  /**
   * Filtre değiştiğinde tetiklenir
   */
  onFilterChange(): void {
    this.currentPage = 1;
    this.loadMembers();
  }

  /**
   * Görünüm modunu değiştir (Seviye 1 <-> Seviye 2)
   */
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'branch' ? 'package' : 'branch';
    this.showPackageDetails = this.viewMode === 'package';
    this.loadMembers();
  }

  /**
   * Sayfa değiştir
   */
  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.loadMembers();
    }
  }

  /**
   * Üyeyi sil - Geliştirilmiş silme sistemi
   */
  deleteMember(member: MemberFilterEnhanced): void {
    // Bu metod akıllı silme sistemi task'ında implement edilecek
    console.log('Delete member:', member);
  }

  /**
   * Üyeliği dondur
   */
  freezeMembership(membershipId: number): void {
    // Bu metod mevcut freeze sistemi ile entegre edilecek
    console.log('Freeze membership:', membershipId);
  }

  /**
   * Üyeliği düzenle
   */
  editMembership(membershipId: number): void {
    // Bu metod mevcut edit sistemi ile entegre edilecek
    console.log('Edit membership:', membershipId);
  }

  /**
   * Branş seçildiğinde paket filtrelerini güncelle
   */
  onBranchSelected(branch: string): void {
    this.selectedBranch = branch;
    this.selectedPackageType = ''; // Paket filtresini sıfırla
    this.onFilterChange();
  }

  /**
   * Paket türü seçildiğinde filtreyi uygula
   */
  onPackageTypeSelected(packageType: string): void {
    this.selectedPackageType = packageType;
    this.onFilterChange();
  }

  /**
   * Filtreleri temizle
   */
  clearFilters(): void {
    this.memberFilterText = '';
    this.genderFilter = '';
    this.selectedBranch = '';
    this.selectedPackageType = '';
    this.minRemainingDays = undefined;
    this.maxRemainingDays = undefined;
    this.currentPage = 1;
    this.loadMembers();
  }

  /**
   * Üyenin toplam branş sayısını al
   */
  getTotalBranchCount(member: MemberFilterEnhanced): number {
    return member.branchSummaries.length;
  }

  /**
   * Üyenin branş listesini string olarak al
   */
  getBranchListString(member: MemberFilterEnhanced): string {
    return member.branchSummaries.map(b => b.branch).join(', ');
  }
}
