<div class="container mt-4">
  <!-- Loading Spinner -->
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
    style="height: 100vh"
  >
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Filtreler ve İstatistikler -->
    <div class="col-md-3">
      <div class="modern-card filter-card slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Gelişmiş Filtreler
          </h5>
          <button 
            class="modern-btn modern-btn-outline-primary modern-btn-sm"
            (click)="toggleViewMode()"
            [title]="viewMode === 'branch' ? 'Paket Detaylarını Göster' : 'Branş Özeti Göster'"
          >
            <fa-icon [icon]="viewMode === 'branch' ? faEye : faEyeSlash"></fa-icon>
            {{ viewMode === 'branch' ? 'Detay' : 'Özet' }}
          </button>
        </div>
        
        <div class="modern-card-body">
          <!-- Görünüm Modu Bilgisi -->
          <div class="view-mode-info mb-3">
            <div class="alert alert-info">
              <i class="fas fa-info-circle me-2"></i>
              <strong>{{ viewMode === 'branch' ? 'Seviye 1: Branş Özeti' : 'Seviye 2: Paket Detayları' }}</strong>
              <br>
              <small>
                {{ viewMode === 'branch' 
                  ? 'Üyelerin branş bazlı toplam gün görünümü' 
                  : 'Her paketin ayrı ayrı detaylı görünümü' }}
              </small>
            </div>
          </div>

          <!-- Arama Kutusu -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-search me-2"></i>
              Arama
            </h6>
            <div class="modern-search-input">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                [ngModel]="memberFilterText"
                (ngModelChange)="searchTextChanged($event)"
                placeholder="Ad, Soyad veya Telefon"
                class="form-control"
              />
            </div>
          </div>

          <!-- Cinsiyet Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-venus-mars me-2"></i>
              Cinsiyet Filtreleri
            </h6>
            <div class="modern-radio-group">
              <div class="modern-radio">
                <input 
                  type="radio" 
                  id="all-gender" 
                  name="gender" 
                  value="" 
                  [(ngModel)]="genderFilter"
                  (change)="onFilterChange()"
                >
                <label for="all-gender">
                  <span class="radio-custom"></span>
                  Tümü ({{ genderCounts.all }})
                </label>
              </div>
              <div class="modern-radio">
                <input 
                  type="radio" 
                  id="male-gender" 
                  name="gender" 
                  value="1" 
                  [(ngModel)]="genderFilter"
                  (change)="onFilterChange()"
                >
                <label for="male-gender">
                  <span class="radio-custom"></span>
                  Erkek ({{ genderCounts.male }})
                </label>
              </div>
              <div class="modern-radio">
                <input 
                  type="radio" 
                  id="female-gender" 
                  name="gender" 
                  value="2" 
                  [(ngModel)]="genderFilter"
                  (change)="onFilterChange()"
                >
                <label for="female-gender">
                  <span class="radio-custom"></span>
                  Kadın ({{ genderCounts.female }})
                </label>
              </div>
            </div>
          </div>

          <!-- Branş Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-dumbbell me-2"></i>
              Branş Filtreleri
            </h6>
            <div class="modern-radio-group">
              <div class="modern-radio">
                <input 
                  type="radio" 
                  id="all-branch" 
                  name="branch" 
                  value="" 
                  [(ngModel)]="selectedBranch"
                  (change)="onBranchSelected('')"
                >
                <label for="all-branch">
                  <span class="radio-custom"></span>
                  Tüm Branşlar
                </label>
              </div>
              <div class="modern-radio" *ngFor="let branch of Object.keys(branchCounts); let i = index">
                <input 
                  type="radio" 
                  [id]="'branch-' + i" 
                  name="branch" 
                  [value]="branch" 
                  [(ngModel)]="selectedBranch"
                  (change)="onBranchSelected(branch)"
                >
                <label [for]="'branch-' + i">
                  <span class="radio-custom"></span>
                  {{ branch }} ({{ branchCounts[branch] }})
                </label>
              </div>
            </div>
          </div>

          <!-- Paket Türü Filtreleri (Seviye 2 için) -->
          <div class="filter-section" *ngIf="selectedBranch">
            <h6 class="filter-title">
              <i class="fas fa-tags me-2"></i>
              Paket Türü Filtreleri
            </h6>
            <div class="modern-radio-group">
              <div class="modern-radio">
                <input 
                  type="radio" 
                  id="all-package" 
                  name="package" 
                  value="" 
                  [(ngModel)]="selectedPackageType"
                  (change)="onPackageTypeSelected('')"
                >
                <label for="all-package">
                  <span class="radio-custom"></span>
                  Tüm Paketler
                </label>
              </div>
              <div class="modern-radio" *ngFor="let mt of membershipTypes.filter(m => m.branch === selectedBranch); let i = index">
                <input 
                  type="radio" 
                  [id]="'package-' + i" 
                  name="package" 
                  [value]="mt.typeName" 
                  [(ngModel)]="selectedPackageType"
                  (change)="onPackageTypeSelected(mt.typeName)"
                >
                <label [for]="'package-' + i">
                  <span class="radio-custom"></span>
                  {{ mt.typeName }}
                </label>
              </div>
            </div>
          </div>

          <!-- Kalan Gün Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-calendar-day me-2"></i>
              Kalan Gün Filtreleri
            </h6>
            <div class="row">
              <div class="col-6">
                <label class="form-label">Min Gün</label>
                <input 
                  type="number" 
                  class="form-control form-control-sm"
                  [(ngModel)]="minRemainingDays"
                  (change)="onFilterChange()"
                  placeholder="0"
                  min="0"
                >
              </div>
              <div class="col-6">
                <label class="form-label">Max Gün</label>
                <input 
                  type="number" 
                  class="form-control form-control-sm"
                  [(ngModel)]="maxRemainingDays"
                  (change)="onFilterChange()"
                  placeholder="365"
                  min="0"
                >
              </div>
            </div>
          </div>

          <!-- Filtreleri Temizle -->
          <div class="filter-section">
            <button 
              class="modern-btn modern-btn-outline-secondary w-100"
              (click)="clearFilters()"
            >
              <i class="fas fa-eraser me-2"></i>
              Filtreleri Temizle
            </button>
          </div>

          <!-- İstatistikler -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-chart-bar me-2"></i>
              İstatistikler
            </h6>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ totalActiveMembers }}</div>
                <div class="stat-label">Toplam Aktif Üye</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ totalItems }}</div>
                <div class="stat-label">Filtrelenen Üye</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Üye Listesi -->
    <div class="col-md-9">
      <div class="modern-card member-list-card fade-in">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faUsers" class="me-2"></fa-icon>
            {{ viewMode === 'branch' ? 'Branş Bazlı Üye Listesi' : 'Paket Detaylı Üye Listesi' }}
          </h5>
          
          <!-- Sayfalama Bilgisi -->
          <div class="pagination-info">
            <span class="text-muted">
              {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalItems) }} / {{ totalItems }}
            </span>
          </div>
        </div>
        
        <div class="modern-card-body">
          <!-- Seviye 1: Branş Bazlı Görünüm -->
          <div *ngIf="viewMode === 'branch'" class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th><i class="fas fa-user me-2"></i>Ad Soyad</th>
                  <th><i class="fas fa-phone me-2"></i>Telefon</th>
                  <th><i class="fas fa-dumbbell me-2"></i>Branşlar</th>
                  <th><i class="fas fa-calendar-day me-2"></i>Toplam Kalan Gün</th>
                  <th><i class="fas fa-cogs me-2"></i>İşlemler</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="zoom-in">
                  <td>
                    <div class="member-name">
                      <div class="modern-avatar" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                        {{ member.name.charAt(0) }}
                      </div>
                      <span>{{ member.name }}</span>
                    </div>
                  </td>
                  <td>{{ member.phoneNumber }}</td>
                  <td>
                    <div class="branch-summary">
                      <span 
                        *ngFor="let branch of member.branchSummaries; let last = last" 
                        class="modern-badge modern-badge-info me-1"
                      >
                        {{ branch.branch }} ({{ branch.totalDaysInBranch }} gün)
                      </span>
                    </div>
                  </td>
                  <td>
                    <span 
                      class="remaining-days"
                      [ngClass]="{
                        'text-success': member.totalRemainingDays > 30, 
                        'text-warning': member.totalRemainingDays <= 30 && member.totalRemainingDays > 7, 
                        'text-danger': member.totalRemainingDays <= 7
                      }"
                    >
                      {{ member.totalRemainingDays }} gün
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm"
                        (click)="deleteMember(member)"
                        title="Üyeyi Sil"
                      >
                        <fa-icon [icon]="faTrashAlt"></fa-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Seviye 2: Paket Detaylı Görünüm -->
          <div *ngIf="viewMode === 'package'" class="package-detail-view">
            <div *ngFor="let member of members" class="member-package-card mb-4">
              <div class="member-header">
                <div class="member-info">
                  <div class="modern-avatar" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                    {{ member.name.charAt(0) }}
                  </div>
                  <div class="member-details">
                    <h6 class="mb-1">{{ member.name }}</h6>
                    <small class="text-muted">{{ member.phoneNumber }}</small>
                  </div>
                </div>
                <div class="member-stats">
                  <span class="badge bg-primary">{{ member.activeMembershipCount }} Aktif Üyelik</span>
                  <span class="badge bg-success">{{ member.totalRemainingDays }} Toplam Gün</span>
                </div>
              </div>
              
              <div class="package-details">
                <div *ngFor="let branch of member.branchSummaries" class="branch-section">
                  <h6 class="branch-title">
                    <i class="fas fa-dumbbell me-2"></i>{{ branch.branch }}
                  </h6>
                  <div class="package-grid">
                    <div *ngFor="let package of branch.packageDetails" class="package-item">
                      <div class="package-header">
                        <span class="package-name">{{ package.typeName }}</span>
                        <span class="package-days" 
                              [ngClass]="{
                                'text-success': package.remainingDays > 30, 
                                'text-warning': package.remainingDays <= 30 && package.remainingDays > 7, 
                                'text-danger': package.remainingDays <= 7
                              }">
                          {{ package.remainingDays }} gün
                        </span>
                      </div>
                      <div class="package-info">
                        <small class="text-muted">
                          {{ package.startDate | date:'dd.MM.yyyy' }} - {{ package.endDate | date:'dd.MM.yyyy' }}
                        </small>
                      </div>
                      <div class="package-actions">
                        <button
                          class="modern-btn modern-btn-outline-primary modern-btn-xs"
                          (click)="editMembership(package.membershipID)"
                          title="Düzenle"
                        >
                          <fa-icon [icon]="faEdit"></fa-icon>
                        </button>
                        <button
                          class="modern-btn modern-btn-outline-info modern-btn-xs"
                          (click)="freezeMembership(package.membershipID)"
                          title="Dondur"
                          *ngIf="!package.isFrozen"
                        >
                          <fa-icon [icon]="faSnowflake"></fa-icon>
                        </button>
                        <button
                          class="modern-btn modern-btn-outline-danger modern-btn-xs"
                          (click)="deleteMember(member)"
                          title="Sil"
                        >
                          <fa-icon [icon]="faTrashAlt"></fa-icon>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sayfalama -->
          <div class="pagination-container" *ngIf="totalPages > 1">
            <nav aria-label="Sayfa navigasyonu">
              <ul class="pagination justify-content-center">
                <li class="page-item" [class.disabled]="currentPage === 1">
                  <button class="page-link" (click)="changePage(currentPage - 1)">Önceki</button>
                </li>
                <li 
                  class="page-item" 
                  *ngFor="let page of [].constructor(totalPages); let i = index"
                  [class.active]="currentPage === i + 1"
                >
                  <button class="page-link" (click)="changePage(i + 1)">{{ i + 1 }}</button>
                </li>
                <li class="page-item" [class.disabled]="currentPage === totalPages">
                  <button class="page-link" (click)="changePage(currentPage + 1)">Sonraki</button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
