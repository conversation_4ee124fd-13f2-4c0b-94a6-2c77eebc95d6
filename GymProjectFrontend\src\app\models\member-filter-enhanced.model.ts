/**
 * İki seviyeli filtreleme sistemi için geliştirilmiş member filter modeli
 * Seviye 1: Branş bazlı toplam görünüm
 * Seviye 2: Paket detay görünümü
 */
export interface MemberFilterEnhanced {
  memberID: number;
  name: string;
  gender: number;
  phoneNumber: string;
  email: string;
  balance: number;
  
  // Branş bazlı toplam bilgiler (Seviye 1)
  branchSummaries: BranchSummary[];
  
  // Toplam kalan gün (tüm branşlar)
  totalRemainingDays: number;
  
  // En son güncelleme tarihi
  lastUpdateDate?: Date;
  
  // Aktif üyelik sayısı
  activeMembershipCount: number;
}

/**
 * Branş bazlı özet bilgiler
 */
export interface BranchSummary {
  branch: string;
  totalDaysInBranch: number;
  activePackageCount: number;
  earliestStartDate?: Date;
  latestEndDate?: Date;
  
  // Bu branştaki paket detayları (Seviye 2 için)
  packageDetails: PackageDetail[];
}

/**
 * Paket detay bilgileri (Seviye 2)
 */
export interface PackageDetail {
  membershipID: number;
  membershipTypeID: number;
  typeName: string;
  branch: string;
  day: number;
  price: number;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  isFrozen: boolean;
  freezeStartDate?: Date;
  freezeEndDate?: Date;
  isFutureStartDate: boolean;
  creationDate?: Date;
  updatedDate?: Date;
}

/**
 * İki seviyeli filtreleme için pagination parametreleri
 */
export interface EnhancedMemberPagingParameters {
  pageNumber: number;
  pageSize: number;
  gender?: number;
  branch?: string;
  membershipTypeFilter?: string; // Paket türü filtresi
  isActive?: boolean;
  searchText?: string;
  showPackageDetails?: boolean; // Seviye 2 detayları göster
  minRemainingDays?: number;
  maxRemainingDays?: number;
}

/**
 * Filtreleme UI için branş ve paket bilgileri
 */
export interface BranchPackageFilter {
  branch: string;
  packages: PackageTypeInfo[];
  totalMembers: number;
}

export interface PackageTypeInfo {
  membershipTypeID: number;
  typeName: string;
  memberCount: number;
  averageRemainingDays: number;
}

/**
 * Silme işlemi için üyelik seçim modeli
 */
export interface MembershipSelectionForDelete {
  memberID: number;
  memberName: string;
  activeMemberships: ActiveMembershipInfo[];
}

export interface ActiveMembershipInfo {
  membershipID: number;
  branch: string;
  typeName: string;
  remainingDays: number;
  endDate: Date;
  totalPaid: number;
  lastPaymentDate: Date;
  paymentMethod: string;
}
