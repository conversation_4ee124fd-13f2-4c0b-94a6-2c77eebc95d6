using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// İki seviyeli filtreleme sistemi için geliştirilmiş member filter DTO
    /// Seviye 1: Branş bazlı toplam görünüm
    /// Seviye 2: Paket detay görünümü
    /// </summary>
    public class MemberFilterEnhancedDto : IDto
    {
        public int MemberID { get; set; }
        public string Name { get; set; }
        public byte Gender { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public decimal Balance { get; set; }
        
        // Branş bazlı toplam bilgiler (Seviye 1)
        public List<BranchSummaryDto> BranchSummaries { get; set; } = new List<BranchSummaryDto>();
        
        // Toplam kalan gün (tüm branşlar)
        public int TotalRemainingDays { get; set; }
        
        // En son güncelleme tarihi
        public DateTime? LastUpdateDate { get; set; }
        
        // Aktif üyelik sayısı
        public int ActiveMembershipCount { get; set; }
    }

    /// <summary>
    /// Branş bazlı özet bilgiler
    /// </summary>
    public class BranchSummaryDto : IDto
    {
        public string Branch { get; set; }
        public int TotalDaysInBranch { get; set; }
        public int ActivePackageCount { get; set; }
        public DateTime? EarliestStartDate { get; set; }
        public DateTime? LatestEndDate { get; set; }
        
        // Bu branştaki paket detayları (Seviye 2 için)
        public List<PackageDetailDto> PackageDetails { get; set; } = new List<PackageDetailDto>();
    }

    /// <summary>
    /// Paket detay bilgileri (Seviye 2)
    /// </summary>
    public class PackageDetailDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string TypeName { get; set; }
        public string Branch { get; set; }
        public int Day { get; set; }
        public decimal Price { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? FreezeStartDate { get; set; }
        public DateTime? FreezeEndDate { get; set; }
        public bool IsFutureStartDate { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }

    /// <summary>
    /// İki seviyeli filtreleme için pagination parametreleri
    /// </summary>
    public class EnhancedMemberPagingParameters : Core.Utilities.Paging.PagingParameters
    {
        public int? Gender { get; set; }
        public string Branch { get; set; } = "";
        public string MembershipTypeFilter { get; set; } = ""; // Paket türü filtresi
        public bool? IsActive { get; set; }
        public string SearchText { get; set; } = "";
        public bool ShowPackageDetails { get; set; } = false; // Seviye 2 detayları göster
        public int? MinRemainingDays { get; set; }
        public int? MaxRemainingDays { get; set; }
    }
}
