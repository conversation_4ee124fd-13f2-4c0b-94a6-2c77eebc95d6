<div class="smart-delete-dialog">
  <!-- Header -->
  <div class="dialog-header">
    <h4 class="dialog-title">
      <fa-icon [icon]="faTrashAlt" class="me-2 text-danger"></fa-icon>
      <PERSON>k<PERSON><PERSON><PERSON>
    </h4>
    <button type="button" class="btn-close" (click)="onCancel()"></button>
  </div>

  <!-- Loading Step -->
  <div *ngIf="step === 'loading'" class="dialog-content text-center">
    <div class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="mt-3">Üyelik bilgileri yükleniyor...</p>
    </div>
  </div>

  <!-- Selection Step -->
  <div *ngIf="step === 'selection'" class="dialog-content">
    <div class="member-info-card mb-4">
      <h5>{{ memberData?.memberName }}</h5>
      <div class="member-stats">
        <span class="badge bg-primary">{{ memberData?.totalActiveMemberships }} Aktif Üyelik</span>
        <span class="badge bg-success">{{ memberData?.totalRemainingDays }} Toplam Gün</span>
        <span class="badge bg-info">{{ formatCurrency(memberData?.totalPaidAmount || 0) }} Toplam Ödeme</span>
      </div>
    </div>

    <div class="alert alert-warning">
      <fa-icon [icon]="faExclamationTriangle" class="me-2"></fa-icon>
      <strong>Dikkat!</strong> Bu üyenin birden fazla aktif üyeliği var. Hangi üyeliği silmek istediğinizi seçin.
    </div>

    <div class="memberships-list">
      <div 
        *ngFor="let membership of memberData?.activeMemberships" 
        class="membership-card"
        [class.selected]="selectedMembershipId === membership.membershipID"
        (click)="onMembershipSelected(membership.membershipID)"
      >
        <div class="membership-header">
          <div class="membership-info">
            <h6 class="mb-1">{{ membership.branch }} - {{ membership.typeName }}</h6>
            <small class="text-muted">{{ formatDate(membership.startDate) }} - {{ formatDate(membership.endDate) }}</small>
          </div>
          <div class="membership-status">
            <span class="remaining-days" 
                  [ngClass]="{
                    'text-success': membership.remainingDays > 30, 
                    'text-warning': membership.remainingDays <= 30 && membership.remainingDays > 7, 
                    'text-danger': membership.remainingDays <= 7
                  }">
              {{ membership.remainingDays }} gün
            </span>
          </div>
        </div>

        <div class="membership-details">
          <div class="row">
            <div class="col-6">
              <small class="text-muted">Toplam Ödenen:</small>
              <div class="fw-bold">{{ formatCurrency(membership.totalPaid) }}</div>
            </div>
            <div class="col-6">
              <small class="text-muted">Kalan Borç:</small>
              <div class="fw-bold text-danger" *ngIf="membership.remainingDebt > 0">
                {{ formatCurrency(membership.remainingDebt) }}
              </div>
              <div class="fw-bold text-success" *ngIf="membership.remainingDebt === 0">
                Borç Yok
              </div>
            </div>
          </div>
        </div>

        <!-- Risk Warning -->
        <div *ngIf="membership.deletionWarningMessage" 
             class="risk-warning mt-2"
             [ngClass]="getRiskClass(membership.deletionRiskLevel)">
          <fa-icon [icon]="getRiskIcon(membership.deletionRiskLevel)" class="me-2"></fa-icon>
          {{ membership.deletionWarningMessage }}
        </div>

        <!-- Payment History -->
        <div *ngIf="membership.payments.length > 0" class="payment-history mt-2">
          <small class="text-muted">Son Ödemeler:</small>
          <div class="payments-list">
            <span 
              *ngFor="let payment of membership.payments.slice(0, 3)" 
              class="badge me-1"
              [ngClass]="getPaymentStatusClass(payment.paymentStatus)"
            >
              {{ formatCurrency(payment.paymentAmount) }} ({{ payment.paymentMethod }})
            </span>
          </div>
        </div>

        <!-- Selection Indicator -->
        <div class="selection-indicator" *ngIf="selectedMembershipId === membership.membershipID">
          <i class="fas fa-check-circle text-primary"></i>
        </div>
      </div>
    </div>

    <!-- Delete Reason -->
    <div class="mt-4">
      <label class="form-label">Silme Nedeni *</label>
      <select class="form-select" [(ngModel)]="deleteReason">
        <option value="">Silme nedenini seçin</option>
        <option *ngFor="let reason of deleteReasons" [value]="reason">{{ reason }}</option>
      </select>
    </div>

    <!-- Admin Notes -->
    <div class="mt-3">
      <label class="form-label">Yönetici Notları</label>
      <textarea 
        class="form-control" 
        rows="3" 
        [(ngModel)]="adminNotes"
        placeholder="İsteğe bağlı notlar..."
      ></textarea>
    </div>
  </div>

  <!-- Confirmation Step -->
  <div *ngIf="step === 'confirmation'" class="dialog-content">
    <div class="confirmation-content">
      <div class="alert alert-danger">
        <fa-icon [icon]="faExclamationTriangle" class="me-2"></fa-icon>
        <strong>Son Uyarı!</strong> Bu işlem geri alınamaz.
      </div>

      <div class="selected-membership-summary" *ngIf="getSelectedMembership()">
        <h6>Silinecek Üyelik:</h6>
        <div class="summary-card">
          <div class="row">
            <div class="col-md-6">
              <strong>{{ getSelectedMembership()?.branch }} - {{ getSelectedMembership()?.typeName }}</strong>
              <br>
              <small>{{ formatDate(getSelectedMembership()?.startDate!) }} - {{ formatDate(getSelectedMembership()?.endDate!) }}</small>
            </div>
            <div class="col-md-6 text-end">
              <div class="text-warning">{{ getSelectedMembership()?.remainingDays }} gün kaldı</div>
              <div class="text-info">{{ formatCurrency(getSelectedMembership()?.totalPaid!) }} ödendi</div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-3">
        <strong>Silme Nedeni:</strong> {{ deleteReason }}
      </div>

      <div class="mt-3" *ngIf="adminNotes">
        <strong>Yönetici Notları:</strong> {{ adminNotes }}
      </div>

      <!-- Refund Option -->
      <div class="mt-4" *ngIf="getSelectedMembership()?.totalPaid! > 0">
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="checkbox" 
            id="processRefund" 
            [(ngModel)]="processRefund"
          >
          <label class="form-check-label" for="processRefund">
            <fa-icon [icon]="faMoneyBillWave" class="me-2"></fa-icon>
            İade işlemi başlat ({{ formatCurrency(getSelectedMembership()?.totalPaid!) }})
          </label>
        </div>
      </div>

      <!-- Final Confirmation -->
      <div class="mt-4">
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="checkbox" 
            id="confirmDeletion" 
            [(ngModel)]="confirmDeletion"
          >
          <label class="form-check-label" for="confirmDeletion">
            <strong>Evet, bu üyeliği silmek istiyorum ve sonuçlarını kabul ediyorum.</strong>
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- Processing Step -->
  <div *ngIf="step === 'processing'" class="dialog-content text-center">
    <div class="processing-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="mt-3">Silme işlemi gerçekleştiriliyor...</p>
      <small class="text-muted">Lütfen bekleyin, bu işlem birkaç saniye sürebilir.</small>
    </div>
  </div>

  <!-- Footer -->
  <div class="dialog-footer">
    <div *ngIf="step === 'selection'" class="d-flex justify-content-between">
      <button type="button" class="btn btn-secondary" (click)="onCancel()">
        İptal
      </button>
      <button 
        type="button" 
        class="btn btn-danger" 
        (click)="startDeletion()"
        [disabled]="!selectedMembershipId || !deleteReason"
      >
        <fa-icon [icon]="faTrashAlt" class="me-2"></fa-icon>
        Devam Et
      </button>
    </div>

    <div *ngIf="step === 'confirmation'" class="d-flex justify-content-between">
      <button type="button" class="btn btn-secondary" (click)="goBack()">
        Geri
      </button>
      <button 
        type="button" 
        class="btn btn-danger" 
        (click)="confirmAndDelete()"
        [disabled]="!confirmDeletion || isLoading"
      >
        <fa-icon [icon]="faTrashAlt" class="me-2"></fa-icon>
        Üyeliği Sil
      </button>
    </div>

    <div *ngIf="step === 'processing'" class="text-center">
      <small class="text-muted">İşlem devam ediyor...</small>
    </div>
  </div>
</div>
