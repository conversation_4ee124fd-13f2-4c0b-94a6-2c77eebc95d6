/* Enhanced Member Filter Component Styles */

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Filter Card Styles */
.filter-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.filter-card:hover {
  transform: translateY(-5px);
}

.filter-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-title {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* View Mode Info */
.view-mode-info .alert {
  padding: 0.75rem;
  margin-bottom: 0;
  border-radius: 8px;
  border: none;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  color: #1565c0;
}

/* Modern Radio Buttons */
.modern-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.modern-radio {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-radio label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-color);
  transition: all 0.3s ease;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  margin: 0;
}

.modern-radio label:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.radio-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 50%;
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.3s ease;
}

.modern-radio input[type="radio"]:checked + label .radio-custom {
  border-color: var(--primary);
  background-color: var(--primary);
}

.modern-radio input[type="radio"]:checked + label .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
}

.modern-radio input[type="radio"]:checked + label {
  color: var(--primary);
  background-color: rgba(var(--primary-rgb), 0.1);
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* Member List Styles */
.member-list-card {
  min-height: 600px;
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--text-muted);
}

/* Branch Summary */
.branch-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.modern-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.modern-badge-info {
  background-color: #e3f2fd;
  color: #1565c0;
}

/* Package Detail View */
.package-detail-view {
  max-height: 70vh;
  overflow-y: auto;
}

.member-package-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.member-package-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.member-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.member-details h6 {
  margin: 0;
  color: var(--text-color);
}

.member-stats {
  display: flex;
  gap: 0.5rem;
}

.member-stats .badge {
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
}

/* Branch Section */
.branch-section {
  margin-bottom: 1.5rem;
}

.branch-title {
  color: var(--primary);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(var(--primary-rgb), 0.2);
}

/* Package Grid */
.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.package-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.package-item:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.package-name {
  font-weight: 600;
  color: var(--text-color);
}

.package-days {
  font-weight: 700;
  font-size: 0.875rem;
}

.package-info {
  margin-bottom: 1rem;
}

.package-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Button Styles */
.modern-btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
}

.modern-btn-outline-primary {
  border: 1px solid var(--primary);
  color: var(--primary);
  background: transparent;
}

.modern-btn-outline-primary:hover {
  background: var(--primary);
  color: white;
}

.modern-btn-outline-info {
  border: 1px solid #17a2b8;
  color: #17a2b8;
  background: transparent;
}

.modern-btn-outline-info:hover {
  background: #17a2b8;
  color: white;
}

.modern-btn-outline-danger {
  border: 1px solid #dc3545;
  color: #dc3545;
  background: transparent;
}

.modern-btn-outline-danger:hover {
  background: #dc3545;
  color: white;
}

.modern-btn-outline-secondary {
  border: 1px solid #6c757d;
  color: #6c757d;
  background: transparent;
}

.modern-btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .member-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .member-stats {
    align-self: stretch;
    justify-content: space-between;
  }

  .package-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Animation Classes */
.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
