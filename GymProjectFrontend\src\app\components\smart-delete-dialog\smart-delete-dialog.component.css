/* Smart Delete Dialog Styles */

.smart-delete-dialog {
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.dialog-title {
  margin: 0;
  color: #dc3545;
  font-weight: 600;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  transition: color 0.3s ease;
}

.btn-close:hover {
  color: #dc3545;
}

/* Content */
.dialog-content {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
}

/* Member Info Card */
.member-info-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
}

.member-info-card h5 {
  margin-bottom: 1rem;
  color: #1565c0;
  font-weight: 600;
}

.member-stats {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.member-stats .badge {
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
}

/* Memberships List */
.memberships-list {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.membership-card {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.membership-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.membership-card.selected {
  border-color: #007bff;
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2);
}

.membership-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.membership-info h6 {
  color: #1565c0;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.membership-status .remaining-days {
  font-weight: 700;
  font-size: 1.1rem;
}

.membership-details {
  margin-bottom: 1rem;
}

.membership-details .fw-bold {
  font-size: 1rem;
  margin-top: 0.25rem;
}

/* Risk Warnings */
.risk-warning {
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
}

.risk-high {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.risk-medium {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.risk-low {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Payment History */
.payment-history {
  border-top: 1px solid #f0f0f0;
  padding-top: 0.75rem;
}

.payments-list {
  margin-top: 0.5rem;
}

.badge-success {
  background-color: #28a745 !important;
}

.badge-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.badge-danger {
  background-color: #dc3545 !important;
}

.badge-secondary {
  background-color: #6c757d !important;
}

/* Selection Indicator */
.selection-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
}

/* Confirmation Content */
.confirmation-content {
  max-height: 500px;
  overflow-y: auto;
}

.selected-membership-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
  border: 1px solid #e0e0e0;
}

/* Loading and Processing */
.loading-container,
.processing-container {
  padding: 2rem;
}

.loading-container p,
.processing-container p {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

/* Footer */
.dialog-footer {
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.dialog-footer .btn {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dialog-footer .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Form Elements */
.form-select,
.form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.form-select:focus,
.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

.form-check-label {
  font-weight: 500;
  cursor: pointer;
}

/* Alert Styles */
.alert {
  border-radius: 8px;
  border: none;
  font-weight: 500;
}

.alert-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
  .smart-delete-dialog {
    max-width: 95vw;
    margin: 1rem;
  }

  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding: 1rem;
  }

  .membership-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .member-stats {
    flex-direction: column;
    align-items: center;
  }

  .summary-card .row {
    text-align: center;
  }

  .summary-card .col-md-6:last-child {
    text-align: center !important;
    margin-top: 0.5rem;
  }
}

/* Animations */
.membership-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar Styling */
.memberships-list::-webkit-scrollbar,
.dialog-content::-webkit-scrollbar,
.confirmation-content::-webkit-scrollbar {
  width: 6px;
}

.memberships-list::-webkit-scrollbar-track,
.dialog-content::-webkit-scrollbar-track,
.confirmation-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.memberships-list::-webkit-scrollbar-thumb,
.dialog-content::-webkit-scrollbar-thumb,
.confirmation-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.memberships-list::-webkit-scrollbar-thumb:hover,
.dialog-content::-webkit-scrollbar-thumb:hover,
.confirmation-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
