using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberDal : EfCompanyEntityRepositoryBase<Member, GymContext>, IMemberDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfMemberDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public MemberDetailWithHistoryDto GetMemberDetailById(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Get member basic information
                var member = context.Members
                    .FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);

                if (member == null)
                {
                    return null;
                }

                // Get memberships with membership type details
                var memberships = (from ms in context.Memberships
                                  join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                  where ms.MemberID == memberId
                                        && ms.CompanyID == companyId
                                        && mt.CompanyID == companyId
                                  select new MembershipHistoryDto
                                  {
                                      MembershipID = ms.MembershipID,
                                      MembershipTypeID = ms.MembershipTypeID,
                                      TypeName = mt.TypeName,
                                      Branch = mt.Branch,
                                      Day = mt.Day,
                                      Price = mt.Price,
                                      StartDate = ms.StartDate,
                                      EndDate = ms.EndDate,
                                      IsActive = ms.IsActive == true,
                                      IsFrozen = ms.IsFrozen,
                                      FreezeStartDate = ms.FreezeStartDate,
                                      FreezeEndDate = ms.FreezeEndDate,
                                      FreezeDays = ms.FreezeDays,
                                      OriginalEndDate = ms.OriginalEndDate,
                                      CreationDate = ms.CreationDate
                                  }).ToList();

                // Get payments with membership type details
                var payments = (from p in context.Payments
                               join ms in context.Memberships on p.MemberShipID equals ms.MembershipID
                               join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                               where ms.MemberID == memberId
                                     && p.CompanyID == companyId
                                     && ms.CompanyID == companyId
                                     && mt.CompanyID == companyId
                               select new PaymentHistoryItemDto
                               {
                                   PaymentID = p.PaymentID,
                                   MembershipID = ms.MembershipID,
                                   MembershipTypeName = mt.TypeName,
                                   Branch = mt.Branch,
                                   PaymentDate = p.PaymentDate,
                                   PaymentAmount = p.PaymentAmount,
                                   PaymentMethod = p.PaymentMethod,
                                   PaymentStatus = p.PaymentStatus
                               }).ToList();

                // Get entry/exit history with membership type details
                var entryExitHistory = (from eh in context.EntryExitHistories
                                       join ms in context.Memberships on eh.MembershipID equals ms.MembershipID
                                       join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                       where ms.MemberID == memberId
                                             && eh.CompanyID == companyId
                                             && ms.CompanyID == companyId
                                             && mt.CompanyID == companyId
                                       select new EntryExitHistoryItemDto
                                       {
                                           EntryExitID = eh.EntryExitID,
                                           MembershipID = ms.MembershipID,
                                           MembershipTypeName = mt.TypeName,
                                           Branch = mt.Branch,
                                           EntryDate = eh.EntryDate,
                                           ExitDate = eh.ExitDate
                                       }).ToList();

                // Get membership freeze history with membership type details
                var freezeHistory = (from fh in context.MembershipFreezeHistory
                                    join ms in context.Memberships on fh.MembershipID equals ms.MembershipID
                                    join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where ms.MemberID == memberId
                                          && fh.CompanyID == companyId
                                          && ms.CompanyID == companyId
                                          && mt.CompanyID == companyId
                                    select new MembershipFreezeHistoryItemDto
                                    {
                                        FreezeHistoryID = fh.FreezeHistoryID,
                                        MembershipID = ms.MembershipID,
                                        MembershipTypeName = mt.TypeName,
                                        Branch = mt.Branch,
                                        StartDate = fh.StartDate,
                                        PlannedEndDate = fh.PlannedEndDate,
                                        ActualEndDate = fh.ActualEndDate,
                                        FreezeDays = fh.FreezeDays,
                                        UsedDays = fh.UsedDays,
                                        CancellationType = fh.CancellationType
                                    }).ToList();

                // Get the last membership end date
                var lastMembershipEndDate = context.Memberships
                    .Where(ms => ms.MemberID == memberId && ms.CompanyID == companyId)
                    .OrderByDescending(ms => ms.EndDate)
                    .Select(ms => (DateTime?)ms.EndDate) // Nullable DateTime'a cast et
                    .FirstOrDefault();

                // Create and return the member detail DTO
                return new MemberDetailWithHistoryDto
                {
                    MemberID = member.MemberID,
                    UserID = member.UserID, // Profil fotoğrafı için eklendi
                    Name = member.Name,
                    Gender = member.Gender,
                    PhoneNumber = member.PhoneNumber,
                    Adress = member.Adress,
                    BirthDate = member.BirthDate,
                    Email = member.Email,
                    IsActive = member.IsActive,
                    ScanNumber = member.ScanNumber,
                    Balance = member.Balance,
                    CreationDate = member.CreationDate,
                    Memberships = memberships,
                    Payments = payments,
                    EntryExitHistory = entryExitHistory,
                    FreezeHistory = freezeHistory,
                    LastMembershipEndDate = lastMembershipEndDate // Son üyelik bitiş tarihini ekle
                };
            }
        }

        public PaginatedResult<Member> GetAllPaginated(MemberPagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var query = context.Members.Where(x => x.IsActive == true && x.CompanyID == companyId);  // IsActive ve CompanyID kontrolü eklendi

                // Filtreleme
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x =>
                        x.Name.Contains(parameters.SearchText) ||
                        x.PhoneNumber.Contains(parameters.SearchText));
                }

                if (parameters.Gender.HasValue)
                {
                    // Gelen int? değerini byte'a cast et
                    byte genderByte = (byte)parameters.Gender.Value;
                    query = query.Where(x => x.Gender == genderByte);
                }

                // Sıralama
                query = query.OrderByDescending(x => x.CreationDate);

                return query.ToPaginatedResult(parameters.PageNumber, parameters.PageSize);
            }
        }

        public List<MemberBirthdayDto> GetUpcomingBirthdays(int days)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var today = DateTime.Now.Date;

                // Doğum günü yaklaşan üyeleri getir
                var result = context.Members
                    .Where(m => m.IsActive == true && m.CompanyID == companyId && m.BirthDate != null)
                    .AsEnumerable() // Sonraki işlemler için hafızaya al
                    .Select(m => new
                    {
                        Member = m,
                        // Doğum gününe kalan gün sayısını hesapla
                        DaysUntilBirthday = CalculateDaysUntilBirthday(m.BirthDate, today)
                    })
                    .Where(x => x.DaysUntilBirthday <= days) // Belirtilen gün sayısı içinde doğum günü olanları filtrele
                    .OrderBy(x => x.DaysUntilBirthday) // Doğum gününe kalan gün sayısına göre sırala
                    .Select(x => new MemberBirthdayDto
                    {
                        MemberID = x.Member.MemberID,
                        Name = x.Member.Name,
                        PhoneNumber = x.Member.PhoneNumber,
                        BirthDate = x.Member.BirthDate
                    })
                    .ToList();

                return result;
            }
        }

        // Doğum gününe kalan gün sayısını hesaplayan yardımcı metod
        private int CalculateDaysUntilBirthday(DateOnly? birthDate, DateTime today)
        {
            if (!birthDate.HasValue) return int.MaxValue; // Doğum tarihi yoksa en sona koy

            var birth = birthDate.Value;

            // Bu yılki doğum günü
            var birthThisYear = new DateTime(today.Year, birth.Month, birth.Day);

            // Eğer bu yılki doğum günü geçtiyse, gelecek yılki doğum gününü hesapla
            if (birthThisYear < today)
            {
                birthThisYear = birthThisYear.AddYears(1);
            }

            // Doğum gününe kalan gün sayısı
            var daysUntil = (int)(birthThisYear - today).TotalDays;

            return daysUntil;
        }

        public PaginatedResult<MemberFilter> GetMemberDetailsPaginated(MemberPagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var baseQuery = from m in context.Members
                                join ms in context.Memberships on m.MemberID equals ms.MemberID
                                join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                where ms.IsActive == true && ms.EndDate > DateTime.Now && ms.IsFrozen==false
                                && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                                select new
                                {
                                    MemberID = m.MemberID,
                                    MembershipID = ms.MembershipID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    Gender = m.Gender,
                                    Branch = mt.Branch,
                                    RemainingDays = ms.StartDate > DateTime.Now
                                        ? EF.Functions.DateDiffDay(ms.StartDate, ms.EndDate) // Gelecekteki başlangıç için
                                        : EF.Functions.DateDiffDay(DateTime.Now, ms.EndDate), // Başlamış üyelik için
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate,
                                    IsActive = ms.IsActive == true,
                                    UpdatedDate = ms.UpdatedDate, // Üyeliğin güncellenme tarihi
                                    CreationDate = ms.CreationDate, // Üyeliğin oluşturulma tarihi
                                    IsFutureStartDate = ms.StartDate > DateTime.Now // Başlangıç tarihi gelecekte mi
                                };

                // Önce verileri hafızaya alıp gruplandırma yapıyoruz
                var groupedQuery = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.Gender, x.Branch })
                    .Select(g => new MemberFilter
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(x => x.MembershipID), // En son eklenen üyeliğin ID'si
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        Gender = g.Key.Gender,
                        Branch = g.Key.Branch,
                        RemainingDays = g.Sum(x => x.RemainingDays), // Kalan günleri topluyoruz
                        StartDate = g.Min(x => x.StartDate), // En erken başlangıç tarihi
                        EndDate = g.Max(x => x.EndDate), // En geç bitiş tarihi
                        IsActive = true,
                        UpdatedDate = g.Max(x => x.UpdatedDate ?? x.CreationDate), // En son güncelleme tarihi veya oluşturma tarihi
                        IsFutureStartDate = g.Any(x => x.IsFutureStartDate) // Herhangi bir üyeliğin başlangıç tarihi gelecekte mi
                    });

                // Filtreleme işlemleri
                var filteredQuery = groupedQuery.AsQueryable();

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    filteredQuery = filteredQuery.Where(x =>
                        x.Name.ToLower().Contains(searchText) ||
                        x.PhoneNumber.Contains(searchText));
                }

                if (parameters.Gender.HasValue)
                {
                    // Gelen int? değerini byte'a cast et
                    byte genderByte = (byte)parameters.Gender.Value;
                    filteredQuery = filteredQuery.Where(x => x.Gender == genderByte);
                }

                if (!string.IsNullOrWhiteSpace(parameters.Branch))
                {
                    filteredQuery = filteredQuery.Where(x => x.Branch == parameters.Branch);
                }

                // Sıralama - Önce UpdatedDate'e göre, sonra MembershipID'ye göre azalan sıralama
                var orderedQuery = filteredQuery
                    .OrderByDescending(x => x.UpdatedDate)
                    .ThenByDescending(x => x.MembershipID);

                // Sayfalama için toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberFilter>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
        }

        public List<MembeFilterDto> GetMemberDetails()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var now = DateTime.Now;

                var query = from m in context.Members
                            join s in context.Memberships
                            on m.MemberID equals s.MemberID
                            join x in context.MembershipTypes
                            on s.MembershipTypeID equals x.MembershipTypeID
                            where s.IsActive == true
                            && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                            && s.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                            && x.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                            select new
                            {
                                m.MemberID,
                                s.MembershipID,
                                x.MembershipTypeID,
                                m.Name,
                                m.Gender,
                                m.PhoneNumber,
                                x.TypeName,
                                x.Branch,
                                s.StartDate,
                                s.EndDate,
                                s.IsActive,
                                m.Balance,
                            };

                var results = query.AsEnumerable();

                var groupedResults = results
                    .GroupBy(r => new { r.MemberID, r.Name, r.Gender, r.PhoneNumber, r.Branch })
                    .Select(g => new MembeFilterDto
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(r => r.MembershipID),
                        MembershipTypeID = g.Max(r => r.MembershipTypeID),
                        Name = g.Key.Name,
                        Gender = g.Key.Gender,
                        PhoneNumber = g.Key.PhoneNumber,
                        Branch = g.Key.Branch,
                        TypeName = string.Join(", ", g.Select(r => r.TypeName).Distinct()),
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r =>
                            (r.EndDate > now) ?
                            (int)Math.Ceiling((r.EndDate - now).TotalDays) : 0),
                        IsActive = true
                    })
                    .ToList();

                return groupedResults;
            }
        }

        public List<GetActiveMemberDto> GetActiveMembers()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from m in context.Members
                             where m.IsActive == true
                             && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                             select new GetActiveMemberDto
                             {
                                 MemberID = m.MemberID,
                                 Name = m.Name,
                                 Gender = m.Gender,
                                 PhoneNumber = m.PhoneNumber,
                                 IsActive = m.IsActive,
                                 Adress = m.Adress,
                                 BirthDate = m.BirthDate,
                                 Email = m.Email,
                                 ScanNumber = m.ScanNumber,
                                 Balance = m.Balance,
                             };
                return result.ToList();
            }
        }

        public List<MemberEntryExitHistoryDto> GetMemberEntryExitHistory()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var currentTime = DateTime.Now;
                var result = from a in context.Members
                             join x in context.Memberships
                             on a.MemberID equals x.MemberID
                             join s in context.EntryExitHistories
                             on x.MembershipID equals s.MembershipID
                             where s.IsActive == true
                             && s.EntryDate.HasValue
                             && EF.Functions.DateDiffMinute(s.EntryDate.Value, currentTime) <= 300
                             && a.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && x.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && s.CompanyID == companyId // Giriş-çıkış kayıtlarının da aynı şirkete ait olduğundan emin ol
                             select new MemberEntryExitHistoryDto
                             {
                                 MemberID = a.MemberID,
                                 MemberName = a.Name,
                                 EntryDate = s.EntryDate,
                                 IsActive = s.IsActive,
                             };
                return result.ToList();
            }
        }

        public List<MemberRemainingDayDto> GetMemberRemainingDay()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var now = DateTime.Now;
                var result = (from a in context.Members
                              join b in context.Memberships on a.MemberID equals b.MemberID
                              join c in context.MembershipTypes on b.MembershipTypeID equals c.MembershipTypeID
                              where b.IsActive == true
                              && a.CompanyID == companyId // Şirket ID'sine göre filtrele
                              && b.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                              && c.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                              select new
                              {
                                  a.MemberID,
                                  a.Name,
                                  a.PhoneNumber,
                                  b.StartDate,
                                  b.EndDate,
                                  c.Branch
                              })
                              .AsEnumerable()
                              .Select(x => new
                              {
                                  MemberID = x.MemberID,
                                  MemberName = x.Name,
                                  PhoneNumber = x.PhoneNumber,
                                  StartDate = x.StartDate,
                                  EndDate = x.EndDate,
                                  RemainingDays = (x.EndDate > now) ? (int)Math.Ceiling((x.EndDate - now).TotalDays) : 0,
                                  Branch = x.Branch
                              })
                              .ToList();

                var groupedResult = result
                    .GroupBy(r => new { r.MemberID, r.Branch })
                    .Select(g => new MemberRemainingDayDto
                    {
                        MemberID = g.Key.MemberID,
                        MemberName = g.First().MemberName,
                        PhoneNumber = g.First().PhoneNumber,
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r => r.RemainingDays),
                        Message = $"Hoşgeldiniz. {g.First().MemberName.ToUpper()}. {g.Key.Branch} branşında toplam {g.Sum(r => r.RemainingDays)} gün kaldı.",
                        Branch = g.Key.Branch
                    })
                    .Where(dto => dto.RemainingDays > 0 && dto.RemainingDays <= 7)
                    .ToList();

                return groupedResult;
            }
        }

        public Member GetMemberByScanNumber(string scanNumber)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                return context.Members.FirstOrDefault(m => m.ScanNumber == scanNumber && m.CompanyID == companyId && m.IsActive == true);
            }
        }

        public GetMemberQRByPhoneNumberDto GetMemberQRByPhoneNumber(string phoneNumber)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var member = context.Members.FirstOrDefault(m => m.PhoneNumber == phoneNumber && m.CompanyID == companyId && m.IsActive == true);

                if (member == null)
                {
                    return null; // Veya uygun bir hata/boş DTO döndür
                }

                var now = DateTime.Now;

                var allMembershipsQuery = context.Memberships
                    .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == companyId);

                var allMemberships = allMembershipsQuery.OrderBy(m => m.StartDate).ToList();

                var membershipTypesQuery = context.MembershipTypes.Where(mt => mt.CompanyID == companyId);

                var membershipTypes = membershipTypesQuery.ToDictionary(mt => mt.MembershipTypeID);

                var consolidatedMemberships = new Dictionary<string, MembershipInfo>();

                var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                if (frozenMembership != null)
                {
                    return new GetMemberQRByPhoneNumberDto
                    {
                        Name = member.Name,
                        ScanNumber = member.ScanNumber,
                        IsFrozen = true,
                        FreezeEndDate = frozenMembership.FreezeEndDate,
                        RemainingDays = "Dondurulmuş",
                        Memberships = new List<MembershipInfo>()
                    };
                }

                foreach (var membership in allMemberships)
                {
                    if (!membershipTypes.ContainsKey(membership.MembershipTypeID))
                        continue;

                    var membershipType = membershipTypes[membership.MembershipTypeID];
                    var branch = membershipType.Branch;

                    if (!consolidatedMemberships.ContainsKey(branch))
                    {
                        consolidatedMemberships[branch] = new MembershipInfo
                        {
                            Branch = branch,
                            StartDate = membership.StartDate,
                            EndDate = membership.EndDate,
                            RemainingDays = 0
                        };
                    }

                    var existingMembership = consolidatedMemberships[branch];

                    if (membership.StartDate < existingMembership.StartDate)
                    {
                        existingMembership.StartDate = membership.StartDate;
                    }

                    if (membership.EndDate > existingMembership.EndDate)
                    {
                        existingMembership.EndDate = membership.EndDate;
                    }

                    int remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);
                    existingMembership.RemainingDays += Math.Max(0, remainingDays);
                }

                string message;
                if (consolidatedMemberships.Count == 0 || consolidatedMemberships.All(m => m.Value.EndDate <= now))
                {
                    message = "Üyeliğinizin Süresi Dolmuştur";
                }
                else if (consolidatedMemberships.All(m => m.Value.StartDate > now))
                {
                    var earliestMembership = consolidatedMemberships.Values.OrderBy(m => m.StartDate).First();
                    message = $"üyeliğinizin başlamasına {(int)(earliestMembership.StartDate - DateTime.Today).TotalDays} gün vardır.";
                }
                else
                {
                    message = "üyeliğiniz aktif durumdadır.";
                }

                return new GetMemberQRByPhoneNumberDto
                {
                    Name = member.Name,
                    ScanNumber = member.ScanNumber,
                    IsFrozen = false,
                    RemainingDays = consolidatedMemberships.Count > 0 ? consolidatedMemberships.Values.Sum(m => m.RemainingDays).ToString() : "Süresi Dolmuş",
                    Memberships = consolidatedMemberships.Values.ToList(),
                    Message = message
                };
            }
        }

       public List<MemberEntryDto> GetTodayEntries(DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;
                // Önce temel sorguyu oluşturalım
                var baseQuery = from m in context.Members
                                join ms in context.Memberships on m.MemberID equals ms.MemberID
                                join eh in context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                                where eh.EntryDate.HasValue && eh.EntryDate.Value.Date == date.Date
                                && ms.IsActive == true
                                && m.CompanyID == companyId // Şirket ID filtresi eklendi
                                && ms.CompanyID == companyId // Şirket ID filtresi eklendi
                                && eh.CompanyID == companyId // Şirket ID filtresi eklendi
                                select new
                                {
                                    MemberID = m.MemberID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    EntryTime = eh.EntryDate.Value,
                                    ExitTime = eh.ExitDate,
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate
                                };

                // Üye bazında gruplayıp, her üyenin tüm üyeliklerini değerlendirelim
                var result = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.EntryTime, x.ExitTime })
                    .Select(g =>
                    {
                        // Üyeliklerin bitiş tarihlerine göre kalan günleri hesaplayalım
                        var totalRemainingDays = context.Memberships
                            .Where(ms => ms.MemberID == g.Key.MemberID && ms.IsActive == true && ms.CompanyID == companyId) // Şirket ID filtresi eklendi
                            .AsEnumerable()
                            .Where(ms => ms.EndDate > now) // Sadece aktif üyelikler
                            .Sum(ms =>
                            {
                                if (ms.StartDate > now)
                                {
                                    return 0; // Henüz başlamamış üyelikler için 0 gün
                                }
                                return (int)Math.Ceiling((ms.EndDate - now).TotalDays);
                            });

                        return new MemberEntryDto
                        {
                            MemberID = g.Key.MemberID,
                            Name = g.Key.Name,
                            PhoneNumber = g.Key.PhoneNumber,
                            EntryTime = g.Key.EntryTime,
                            ExitTime = g.Key.ExitTime,
                            RemainingDays = totalRemainingDays
                        };
                    })
                    .OrderByDescending(x => x.EntryTime)
                    .ToList();

                return result;
            }
        }

        public PaginatedResult<MemberFilterEnhancedDto> GetMemberDetailsEnhancedPaginated(EnhancedMemberPagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                // Temel sorgu: Aktif üyeler ve üyelikleri
                var baseQuery = from m in context.Members
                                join ms in context.Memberships on m.MemberID equals ms.MemberID
                                join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                where ms.IsActive == true && ms.EndDate > now && !ms.IsFrozen
                                && m.CompanyID == companyId
                                && ms.CompanyID == companyId
                                && mt.CompanyID == companyId
                                select new
                                {
                                    MemberID = m.MemberID,
                                    MembershipID = ms.MembershipID,
                                    MembershipTypeID = ms.MembershipTypeID,
                                    Name = m.Name,
                                    Gender = m.Gender,
                                    PhoneNumber = m.PhoneNumber,
                                    Email = m.Email,
                                    Balance = m.Balance,
                                    Branch = mt.Branch,
                                    TypeName = mt.TypeName,
                                    Day = mt.Day,
                                    Price = mt.Price,
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate,
                                    IsFrozen = ms.IsFrozen,
                                    FreezeStartDate = ms.FreezeStartDate,
                                    FreezeEndDate = ms.FreezeEndDate,
                                    CreationDate = ms.CreationDate,
                                    UpdatedDate = ms.UpdatedDate,
                                    RemainingDays = ms.StartDate > now ? 0 : (int)Math.Ceiling((ms.EndDate - now).TotalDays)
                                };

                // Filtreleme uygula
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    baseQuery = baseQuery.Where(x =>
                        x.Name.Contains(parameters.SearchText) ||
                        x.PhoneNumber.Contains(parameters.SearchText));
                }

                if (parameters.Gender.HasValue)
                {
                    byte genderByte = (byte)parameters.Gender.Value;
                    baseQuery = baseQuery.Where(x => x.Gender == genderByte);
                }

                if (!string.IsNullOrWhiteSpace(parameters.Branch))
                {
                    baseQuery = baseQuery.Where(x => x.Branch.ToLower() == parameters.Branch.ToLower());
                }

                if (!string.IsNullOrWhiteSpace(parameters.MembershipTypeFilter))
                {
                    baseQuery = baseQuery.Where(x => x.TypeName.ToLower().Contains(parameters.MembershipTypeFilter.ToLower()));
                }

                if (parameters.MinRemainingDays.HasValue)
                {
                    baseQuery = baseQuery.Where(x => x.RemainingDays >= parameters.MinRemainingDays.Value);
                }

                if (parameters.MaxRemainingDays.HasValue)
                {
                    baseQuery = baseQuery.Where(x => x.RemainingDays <= parameters.MaxRemainingDays.Value);
                }

                // Verileri hafızaya al ve gruplandır
                var queryResults = baseQuery.AsEnumerable();

                var groupedData = queryResults
                    .GroupBy(x => new { x.MemberID, x.Name, x.Gender, x.PhoneNumber, x.Email, x.Balance })
                    .Select(memberGroup => new MemberFilterEnhancedDto
                    {
                        MemberID = memberGroup.Key.MemberID,
                        Name = memberGroup.Key.Name,
                        Gender = memberGroup.Key.Gender,
                        PhoneNumber = memberGroup.Key.PhoneNumber,
                        Email = memberGroup.Key.Email ?? "",
                        Balance = memberGroup.Key.Balance,
                        TotalRemainingDays = memberGroup.Sum(x => x.RemainingDays),
                        ActiveMembershipCount = memberGroup.Count(),
                        LastUpdateDate = memberGroup.Max(x => x.UpdatedDate ?? x.CreationDate),
                        BranchSummaries = memberGroup
                            .GroupBy(x => x.Branch)
                            .Select(branchGroup => new BranchSummaryDto
                            {
                                Branch = branchGroup.Key,
                                TotalDaysInBranch = branchGroup.Sum(x => x.RemainingDays),
                                ActivePackageCount = branchGroup.Count(),
                                EarliestStartDate = branchGroup.Min(x => x.StartDate),
                                LatestEndDate = branchGroup.Max(x => x.EndDate),
                                PackageDetails = parameters.ShowPackageDetails ? branchGroup.Select(x => new PackageDetailDto
                                {
                                    MembershipID = x.MembershipID,
                                    MembershipTypeID = x.MembershipTypeID,
                                    TypeName = x.TypeName,
                                    Branch = x.Branch,
                                    Day = x.Day,
                                    Price = x.Price,
                                    StartDate = x.StartDate,
                                    EndDate = x.EndDate,
                                    RemainingDays = x.RemainingDays,
                                    IsFrozen = x.IsFrozen,
                                    FreezeStartDate = x.FreezeStartDate,
                                    FreezeEndDate = x.FreezeEndDate,
                                    IsFutureStartDate = x.StartDate > now,
                                    CreationDate = x.CreationDate,
                                    UpdatedDate = x.UpdatedDate
                                }).ToList() : new List<PackageDetailDto>()
                            }).ToList()
                    });

                // Sıralama
                var orderedQuery = groupedData
                    .OrderByDescending(x => x.LastUpdateDate)
                    .ThenByDescending(x => x.TotalRemainingDays);

                // Sayfalama için toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberFilterEnhancedDto>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
        }

        public MemberActiveMembershipsDto GetMemberActiveMemberships(int memberId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                // Üye bilgilerini al
                var member = context.Members.FirstOrDefault(m =>
                    m.MemberID == memberId &&
                    m.CompanyID == companyId &&
                    m.IsActive == true);

                if (member == null)
                {
                    return null;
                }

                // Aktif üyelikleri al
                var activeMembershipsQuery = from ms in context.Memberships
                                           join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                           where ms.MemberID == memberId
                                           && ms.IsActive == true
                                           && ms.EndDate > now
                                           && ms.CompanyID == companyId
                                           && mt.CompanyID == companyId
                                           select new
                                           {
                                               MembershipID = ms.MembershipID,
                                               MembershipTypeID = ms.MembershipTypeID,
                                               Branch = mt.Branch,
                                               TypeName = mt.TypeName,
                                               Day = mt.Day,
                                               Price = mt.Price,
                                               StartDate = ms.StartDate,
                                               EndDate = ms.EndDate,
                                               IsFrozen = ms.IsFrozen,
                                               FreezeStartDate = ms.FreezeStartDate,
                                               FreezeEndDate = ms.FreezeEndDate,
                                               CreationDate = ms.CreationDate,
                                               UpdatedDate = ms.UpdatedDate,
                                               RemainingDays = ms.StartDate > now ? 0 : (int)Math.Ceiling((ms.EndDate - now).TotalDays)
                                           };

                var activeMemberships = activeMembershipsQuery.ToList();

                // Her üyelik için ödeme bilgilerini al
                var membershipDetails = new List<ActiveMembershipDetailDto>();
                decimal totalPaidAmount = 0;

                foreach (var membership in activeMemberships)
                {
                    // Bu üyeliğe ait ödemeleri al
                    var payments = (from p in context.Payments
                                  where p.MemberShipID == membership.MembershipID
                                  && p.IsActive == true
                                  select new PaymentInfoDto
                                  {
                                      PaymentID = p.PaymentID,
                                      PaymentAmount = p.PaymentAmount,
                                      PaymentMethod = p.PaymentMethod,
                                      PaymentStatus = p.PaymentStatus,
                                      PaymentDate = p.PaymentDate,
                                      CanBeRefunded = p.PaymentDate > now.AddDays(-30) && p.PaymentStatus == "Completed" // Son 30 gün içinde ve tamamlanmış
                                  }).ToList();

                    var totalPaid = payments.Sum(p => p.PaymentAmount);
                    totalPaidAmount += totalPaid;

                    // Kalan borç hesapla (RemainingDebts tablosundan)
                    var remainingDebt = context.RemainingDebts
                        .Where(rd => payments.Any(p => p.PaymentID == rd.PaymentID) && rd.IsActive == true)
                        .Sum(rd => rd.RemainingAmount);

                    // Risk analizi
                    var isRecentlyCreated = membership.CreationDate > now.AddDays(-7);
                    var hasPendingPayments = payments.Any(p => p.PaymentStatus == "Pending");

                    string riskLevel = "LOW";
                    string warningMessage = "";

                    if (hasPendingPayments)
                    {
                        riskLevel = "HIGH";
                        warningMessage = "Bu üyelikte bekleyen ödemeler var. Silme işlemi ödeme durumunu etkileyebilir.";
                    }
                    else if (isRecentlyCreated)
                    {
                        riskLevel = "MEDIUM";
                        warningMessage = "Bu üyelik son 7 gün içinde oluşturulmuş. Silme işleminden emin misiniz?";
                    }
                    else if (totalPaid > 0)
                    {
                        riskLevel = "MEDIUM";
                        warningMessage = $"Bu üyelik için toplam {totalPaid:C} ödeme yapılmış. İade işlemi gerekebilir.";
                    }

                    membershipDetails.Add(new ActiveMembershipDetailDto
                    {
                        MembershipID = membership.MembershipID,
                        MembershipTypeID = membership.MembershipTypeID,
                        Branch = membership.Branch,
                        TypeName = membership.TypeName,
                        Day = membership.Day,
                        Price = membership.Price,
                        StartDate = membership.StartDate,
                        EndDate = membership.EndDate,
                        RemainingDays = membership.RemainingDays,
                        IsFrozen = membership.IsFrozen,
                        FreezeStartDate = membership.FreezeStartDate,
                        FreezeEndDate = membership.FreezeEndDate,
                        CreationDate = membership.CreationDate ?? DateTime.Now,
                        UpdatedDate = membership.UpdatedDate,
                        Payments = payments,
                        TotalPaid = totalPaid,
                        RemainingDebt = remainingDebt,
                        LastPaymentDate = payments.OrderByDescending(p => p.PaymentDate).FirstOrDefault()?.PaymentDate,
                        LastPaymentMethod = payments.OrderByDescending(p => p.PaymentDate).FirstOrDefault()?.PaymentMethod,
                        HasPendingPayments = hasPendingPayments,
                        IsRecentlyCreated = isRecentlyCreated,
                        DeletionRiskLevel = riskLevel,
                        DeletionWarningMessage = warningMessage
                    });
                }

                return new MemberActiveMembershipsDto
                {
                    MemberID = member.MemberID,
                    MemberName = member.Name,
                    PhoneNumber = member.PhoneNumber,
                    Email = member.Email ?? "",
                    Balance = member.Balance,
                    ActiveMemberships = membershipDetails.OrderBy(m => m.Branch).ThenBy(m => m.TypeName).ToList(),
                    TotalActiveMemberships = membershipDetails.Count,
                    TotalRemainingDays = membershipDetails.Sum(m => m.RemainingDays),
                    TotalPaidAmount = totalPaidAmount
                };
            }
        }

        public List<MemberEntryDto> GetMemberEntriesByName(string searchText)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from m in context.Members
                             join ms in context.Memberships on m.MemberID equals ms.MemberID
                             join eh in context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                             where (m.Name.Contains(searchText) || m.PhoneNumber.Contains(searchText))
                                   && eh.EntryDate.HasValue
                                   && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                   && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                   && eh.CompanyID == companyId // Giriş-çıkış kayıtlarının da aynı şirkete ait olduğundan emin ol
                             orderby eh.EntryDate descending // Tarihe göre tersten sıralama
                             select new MemberEntryDto
                             {
                                 MemberID = m.MemberID,
                                 Name = m.Name,
                                 PhoneNumber = m.PhoneNumber,
                                 EntryTime = eh.EntryDate.Value,
                                 ExitTime = eh.ExitDate
                             };

                return result.ToList();
            }
        }
    } // EfMemberDal sınıfı kapanışı
} // namespace kapanışı
