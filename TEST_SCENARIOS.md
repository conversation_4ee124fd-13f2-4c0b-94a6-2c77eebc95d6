# 🧪 GYM PROJECT - TEST SENARYOLARI VE GERİYE UYUMLULUK KONTROLÜ

## 📋 GENEL TEST STRATEJİSİ

Bu dokümanda, yeni geliştirilen özelliklerin test senaryoları ve geriye uyumluluk kontrolleri yer almaktadır.

---

## 🔄 GERİYE UYUMLULUK KONTROL LİSTESİ

### ✅ **1. API Endpoint'leri**
- [ ] Mevcut `/api/member/getmemberdetailspaginated` endpoint'i çalışıyor
- [ ] Mevcut `/api/membership/add` endpoint'i çalışıyor  
- [ ] Mevcut `/api/membership/delete` endpoint'i çalışıyor
- [ ] Tüm response formatları aynı kalıyor

### ✅ **2. Database Şeması**
- [ ] Mevcut tablolar değişmedi
- [ ] Mevcut kolonlar korundu
- [ ] Sadece yeni indexler eklendi
- [ ] Mevcut veriler etkilenmedi

### ✅ **3. Frontend Component'leri**
- [ ] Eski `member-filter.component` çalışıyor
- [ ] Mevcut routing'ler korundu
- [ ] Eski filtreleme sistemi çalışıyor

---

## 🆕 YENİ ÖZELLİKLER TEST SENARYOLARI

### **1. İKİ SEVİYELİ FİLTRELEME SİSTEMİ**

#### **Test Senaryosu 1.1: Branş Bazlı Görünüm (Seviye 1)**
```
GIVEN: Üyenin birden fazla branşa üyeliği var
  - Ahmet: Fitness (30 gün) + Crossfit (60 gün)
WHEN: Enhanced member filter açılır
THEN: 
  - Ahmet toplam 90 gün olarak görünür
  - Branşlar: "Fitness, Crossfit" şeklinde listelenir
  - Toplam aktif üyelik sayısı: 2
```

#### **Test Senaryosu 1.2: Paket Detay Görünümü (Seviye 2)**
```
GIVEN: Aynı branşta farklı paketleri olan üye
  - Mehmet: Fitness 1 Aylık (15 gün) + Fitness 3 Aylık (75 gün)
WHEN: "Detay" moduna geçilir
THEN:
  - Her paket ayrı ayrı görünür
  - 1 Aylık: 15 gün kaldı
  - 3 Aylık: 75 gün kaldı
  - Her pakette ayrı işlem butonları var
```

#### **Test Senaryosu 1.3: Branş + Paket Filtreleme**
```
GIVEN: Karışık üyelikler
WHEN: "Fitness" branşı seçilir
THEN: Sadece Fitness üyelikleri görünür
WHEN: "1 Aylık" paket filtresi eklenir  
THEN: Sadece Fitness 1 Aylık paketler görünür
```

### **2. AKILLI SİLME SİSTEMİ**

#### **Test Senaryosu 2.1: Çoklu Üyelik Silme**
```
GIVEN: Üyenin 3 aktif üyeliği var
  - Fitness 1 Aylık (500₺ ödendi)
  - Crossfit 2 Aylık (800₺ ödendi)  
  - Pilates 1 Aylık (300₺ ödendi)
WHEN: "Sil" butonuna tıklanır
THEN:
  - Modal açılır
  - 3 üyelik listelenir
  - Her birinin ödeme bilgileri görünür
  - Risk seviyeleri gösterilir
```

#### **Test Senaryosu 2.2: Risk Analizi**
```
GIVEN: Farklı risk seviyelerinde üyelikler
WHEN: Silme modalı açılır
THEN:
  - Yeni üyelik (7 gün): MEDIUM risk, sarı uyarı
  - Bekleyen ödeme: HIGH risk, kırmızı uyarı
  - Normal üyelik: LOW risk, mavi bilgi
```

#### **Test Senaryosu 2.3: İade İşlemi**
```
GIVEN: 1000₺ ödenmiş üyelik
WHEN: Silme işlemi yapılır
AND: "İade işlemi başlat" seçilir
THEN:
  - İade miktarı gösterilir
  - Onay sonrası işlem tamamlanır
  - İade kaydı oluşur
```

### **3. PERFORMANS OPTİMİZASYONU**

#### **Test Senaryosu 3.1: Büyük Veri Seti Performansı**
```
GIVEN: 10,000+ üye kaydı
WHEN: Member filter açılır
THEN:
  - Sayfa 2 saniyeden az yüklenir
  - Filtreleme 1 saniyeden az sürer
  - Pagination sorunsuz çalışır
```

#### **Test Senaryosu 3.2: Çok Kiracılı Performans**
```
GIVEN: 100+ farklı salon (CompanyID)
WHEN: Aynı anda filtreleme yapılır
THEN:
  - Her salon sadece kendi verilerini görür
  - Performans düşüşü olmaz
  - Cache sistemi çalışır
```

#### **Test Senaryosu 3.3: Cache Sistemi**
```
GIVEN: İlk kez veri yüklendi
WHEN: Aynı filtre tekrar uygulanır
THEN:
  - Veriler cache'den gelir
  - Yükleme süresi %80 azalır
  - Database sorgu sayısı azalır
```

---

## 🔧 MANUEL TEST ADIMLARI

### **Adım 1: Geriye Uyumluluk Testi**
1. Eski member-filter sayfasını aç
2. Mevcut filtreleri test et
3. Üye ekleme/silme işlemlerini test et
4. Hiçbir hata olmamalı

### **Adım 2: Yeni Özellik Testi**
1. Enhanced member filter sayfasını aç
2. Seviye 1 → Seviye 2 geçişini test et
3. Akıllı silme modalını test et
4. Performans ölçümlerini al

### **Adım 3: Entegrasyon Testi**
1. Yeni üyelik ekle
2. Farklı branş/paket kombinasyonları dene
3. Silme işlemlerini test et
4. Cache temizleme test et

---

## 📊 PERFORMANS METRIKLERI

### **Beklenen İyileştirmeler:**
- **Sayfa Yükleme:** %300-500 hızlanma
- **Filtreleme:** %200-300 hızlanma  
- **Database Sorguları:** %400-600 azalma
- **Memory Kullanımı:** %20-30 artış (cache nedeniyle)

### **Ölçüm Araçları:**
- Browser Developer Tools
- SQL Server Profiler
- Application Insights
- Custom Performance Counters

---

## 🚨 KRİTİK TEST NOKTALARI

### **1. Veri Tutarlılığı**
- Aynı üyenin farklı görünümlerde aynı toplam günü göstermesi
- Silme işlemlerinde veri kaybı olmaması
- Cache ile database arasında tutarsızlık olmaması

### **2. Güvenlik**
- Multi-tenant izolasyonun korunması
- Yetki kontrollerinin çalışması
- SQL injection korumasının devam etmesi

### **3. Performans**
- Büyük veri setlerinde timeout olmaması
- Memory leak olmaması
- Database connection pool'unun tükenmemesi

---

## 🔄 ROLLBACK PLANI

Eğer kritik bir sorun çıkarsa:

### **Adım 1: Hızlı Rollback**
1. Yeni endpoint'leri devre dışı bırak
2. Eski component'leri aktif et
3. Cache'i temizle

### **Adım 2: Database Rollback**
1. Yeni indexleri kaldır (opsiyonel)
2. Mevcut istatistikleri güncelle
3. Performance monitoring'i kontrol et

### **Adım 3: Monitoring**
1. Error log'ları takip et
2. Performance metrikleri izle
3. User feedback topla

---

## ✅ TEST TAMAMLAMA KRİTERLERİ

Aşağıdaki tüm kriterler sağlandığında test tamamlanmış sayılır:

- [ ] Tüm geriye uyumluluk testleri geçti
- [ ] Yeni özellikler beklendiği gibi çalışıyor
- [ ] Performans hedefleri karşılandı
- [ ] Güvenlik kontrolleri başarılı
- [ ] User acceptance testleri tamamlandı
- [ ] Production deployment hazır

---

## 📞 DESTEK VE İLETİŞİM

Test sırasında sorun yaşanırsa:
1. Error log'ları kaydet
2. Repro adımlarını belirle  
3. Screenshot/video al
4. Development team'e ilet

**Test Sorumlusu:** Development Team
**Test Süresi:** 2-3 gün
**Go-Live Tarihi:** Test tamamlandıktan sonra
