using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Akıllı silme sistemi için üyenin aktif üyeliklerini gösteren DTO
    /// </summary>
    public class MemberActiveMembershipsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public decimal Balance { get; set; }
        
        // Aktif üyelikler listesi
        public List<ActiveMembershipDetailDto> ActiveMemberships { get; set; } = new List<ActiveMembershipDetailDto>();
        
        // Toplam aktif üyelik sayısı
        public int TotalActiveMemberships { get; set; }
        
        // Toplam kalan gün
        public int TotalRemainingDays { get; set; }
        
        // Toplam ödenen miktar
        public decimal TotalPaidAmount { get; set; }
    }

    /// <summary>
    /// Aktif üyelik detay bilgileri
    /// </summary>
    public class ActiveMembershipDetailDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public int Day { get; set; }
        public decimal Price { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? FreezeStartDate { get; set; }
        public DateTime? FreezeEndDate { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        
        // Ödeme bilgileri
        public List<PaymentInfoDto> Payments { get; set; } = new List<PaymentInfoDto>();
        public decimal TotalPaid { get; set; }
        public decimal RemainingDebt { get; set; }
        public DateTime? LastPaymentDate { get; set; }
        public string LastPaymentMethod { get; set; }
        
        // Risk analizi
        public bool HasPendingPayments { get; set; }
        public bool IsRecentlyCreated { get; set; } // Son 7 gün içinde oluşturulmuş
        public string DeletionRiskLevel { get; set; } // "LOW", "MEDIUM", "HIGH"
        public string DeletionWarningMessage { get; set; }
    }

    /// <summary>
    /// Ödeme bilgileri
    /// </summary>
    public class PaymentInfoDto : IDto
    {
        public int PaymentID { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string PaymentStatus { get; set; }
        public DateTime PaymentDate { get; set; }
        public bool CanBeRefunded { get; set; } // İade edilebilir mi?
    }

    /// <summary>
    /// Silme işlemi için request DTO
    /// </summary>
    public class MembershipDeleteRequestDto : IDto
    {
        public int MembershipID { get; set; }
        public string DeleteReason { get; set; }
        public bool ConfirmDeletion { get; set; }
        public bool ProcessRefund { get; set; } // İade işlemi yapılsın mı?
        public string AdminNotes { get; set; }
    }

    /// <summary>
    /// Silme işlemi sonuç DTO
    /// </summary>
    public class MembershipDeleteResultDto : IDto
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int DeletedMembershipID { get; set; }
        public decimal RefundAmount { get; set; }
        public bool RefundProcessed { get; set; }
        public List<string> Warnings { get; set; } = new List<string>();
        public DateTime DeletionDate { get; set; }
    }
}
